<?php
ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-edit"></i>
                        Edit Invoice #<?= e($invoice->invoice_number) ?>
                    </h3>
                    <div class="card-tools">
                        <a href="<?= url('/invoices/' . $invoice->id) ?>" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Invoice
                        </a>
                    </div>
                </div>

                <form id="invoiceForm" action="<?= url('/invoices/' . $invoice->id) ?>" method="POST">
                    <input type="hidden" name="_method" value="PUT">

                    <div class="card-body">
                        <!-- Invoice Header -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5>Invoice Details</h5>
                                <div class="mb-3">
                                    <label for="invoice_number" class="form-label">Invoice Number</label>
                                    <input type="text" name="invoice_number" id="invoice_number"
                                           class="form-control" value="<?= e($invoice->invoice_number) ?>" readonly>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="invoice_date" class="form-label">Invoice Date</label>
                                            <input type="date" name="invoice_date" id="invoice_date"
                                                   class="form-control" value="<?= e($invoice->invoice_date) ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="due_date" class="form-label">Due Date</label>
                                            <input type="date" name="due_date" id="due_date"
                                                   class="form-control" value="<?= e($invoice->due_date) ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <h5>Company Information</h5>
                                <div class="mb-3">
                                    <label for="company_name" class="form-label">Company Name</label>
                                    <input type="text" name="company_name" id="company_name"
                                           class="form-control" value="<?= e($invoice->company_name) ?>">
                                </div>
                                <div class="mb-3">
                                    <label for="company_address" class="form-label">Company Address</label>
                                    <textarea name="company_address" id="company_address"
                                              class="form-control" rows="3"><?= e($invoice->company_address) ?></textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="company_phone" class="form-label">Phone</label>
                                            <input type="text" name="company_phone" id="company_phone"
                                                   class="form-control" value="<?= e($invoice->company_phone) ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="company_email" class="form-label">Email</label>
                                            <input type="email" name="company_email" id="company_email"
                                                   class="form-control" value="<?= e($invoice->company_email) ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <!-- Client Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5>Bill To</h5>
                                <div class="mb-3">
                                    <label for="client_id" class="form-label">Select Client</label>
                                    <select name="client_id" id="client_id" class="form-control">
                                        <option value="">Select existing client or enter manually</option>
                                        <?php if (isset($clients)): ?>
                                            <?php foreach ($clients as $client): ?>
                                                <option value="<?= e($client['id']) ?>"
                                                        <?= $invoice->client_id == $client['id'] ? 'selected' : '' ?>
                                                        data-name="<?= e($client['name']) ?>"
                                                        data-email="<?= e($client['email']) ?>"
                                                        data-phone="<?= e($client['phone']) ?>"
                                                        data-address="<?= e($client['address']) ?>">
                                                    <?= e($client['name']) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="client_name" class="form-label">Client Name *</label>
                                    <input type="text" name="client_name" id="client_name"
                                           class="form-control" value="<?= e($invoice->client_name) ?>" required>
                                </div>
                                <div class="mb-3">
                                    <label for="client_email" class="form-label">Client Email</label>
                                    <input type="email" name="client_email" id="client_email"
                                           class="form-control" value="<?= e($invoice->client_email) ?>">
                                </div>
                                <div class="mb-3">
                                    <label for="client_phone" class="form-label">Client Phone</label>
                                    <input type="text" name="client_phone" id="client_phone"
                                           class="form-control" value="<?= e($invoice->client_phone) ?>">
                                </div>
                                <div class="mb-3">
                                    <label for="client_address" class="form-label">Client Address</label>
                                    <textarea name="client_address" id="client_address"
                                              class="form-control" rows="3"><?= e($invoice->client_address) ?></textarea>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <!-- Invoice Items -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5>Invoice Items</h5>
                                <button type="button" class="btn btn-success btn-sm" onclick="addInvoiceItem()">
                                    <i class="fas fa-plus"></i> Add Item
                                </button>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-bordered" id="invoiceItemsTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="40%">Description</th>
                                            <th width="15%">Quantity</th>
                                            <th width="20%">Unit Price (₹)</th>
                                            <th width="20%">Total (₹)</th>
                                            <th width="5%">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody id="invoiceItemsBody">
                                        <?php if (empty($items)): ?>
                                            <tr class="invoice-item-row">
                                                <td>
                                                    <input type="text" name="items[0][description]"
                                                           class="form-control" placeholder="Item description" required>
                                                </td>
                                                <td>
                                                    <input type="number" name="items[0][quantity]"
                                                           class="form-control quantity-input"
                                                           value="1" min="0.01" step="0.01" required>
                                                </td>
                                                <td>
                                                    <input type="number" name="items[0][unit_price]"
                                                           class="form-control unit-price-input"
                                                           min="0.01" step="0.01" required>
                                                </td>
                                                <td>
                                                    <input type="number" name="items[0][total_price]"
                                                           class="form-control total-price-input"
                                                           readonly>
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-danger btn-sm"
                                                            onclick="removeInvoiceItem(this)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php else: ?>
                                            <?php foreach ($items as $index => $item): ?>
                                                <tr class="invoice-item-row">
                                                    <td>
                                                        <input type="text" name="items[<?= $index ?>][description]"
                                                               class="form-control" value="<?= e($item->description) ?>" required>
                                                    </td>
                                                    <td>
                                                        <input type="number" name="items[<?= $index ?>][quantity]"
                                                               class="form-control quantity-input"
                                                               value="<?= e($item->quantity) ?>" min="0.01" step="0.01" required>
                                                    </td>
                                                    <td>
                                                        <input type="number" name="items[<?= $index ?>][unit_price]"
                                                               class="form-control unit-price-input"
                                                               value="<?= e($item->unit_price) ?>" min="0.01" step="0.01" required>
                                                    </td>
                                                    <td>
                                                        <input type="number" name="items[<?= $index ?>][total_price]"
                                                               class="form-control total-price-input"
                                                               value="<?= e($item->total_price) ?>" readonly>
                                                    </td>
                                                    <td>
                                                        <button type="button" class="btn btn-danger btn-sm"
                                                                onclick="removeInvoiceItem(this)">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Invoice Totals -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">Notes</label>
                                    <textarea name="notes" id="notes" class="form-control" rows="3"
                                              placeholder="Additional notes or comments"><?= e($invoice->notes) ?></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="terms" class="form-label">Terms & Conditions</label>
                                    <textarea name="terms" id="terms" class="form-control" rows="3"
                                              placeholder="Payment terms and conditions"><?= e($invoice->terms) ?></textarea>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">Invoice Summary</h6>

                                        <div class="row mb-2">
                                            <div class="col-6">Subtotal:</div>
                                            <div class="col-6 text-end">
                                                ₹<span id="subtotalDisplay"><?= number_format($invoice->subtotal, 2) ?></span>
                                                <input type="hidden" name="subtotal" id="subtotal" value="<?= $invoice->subtotal ?>">
                                            </div>
                                        </div>

                                        <div class="row mb-2">
                                            <div class="col-6">
                                                <label for="discount_amount">Discount (₹):</label>
                                            </div>
                                            <div class="col-6">
                                                <input type="number" name="discount_amount" id="discount_amount"
                                                       class="form-control form-control-sm" value="<?= $invoice->discount_amount ?>" min="0" step="0.01">
                                            </div>
                                        </div>

                                        <div class="row mb-2">
                                            <div class="col-6">
                                                <label for="tax_rate">Tax Rate (%):</label>
                                            </div>
                                            <div class="col-6">
                                                <input type="number" name="tax_rate" id="tax_rate"
                                                       class="form-control form-control-sm" value="<?= $invoice->tax_rate ?>" min="0" max="100" step="0.01">
                                            </div>
                                        </div>

                                        <div class="row mb-2">
                                            <div class="col-6">Tax Amount:</div>
                                            <div class="col-6 text-end">
                                                ₹<span id="taxAmountDisplay"><?= number_format($invoice->tax_amount, 2) ?></span>
                                                <input type="hidden" name="tax_amount" id="tax_amount" value="<?= $invoice->tax_amount ?>">
                                            </div>
                                        </div>

                                        <hr>

                                        <div class="row">
                                            <div class="col-6"><strong>Total Amount:</strong></div>
                                            <div class="col-6 text-end">
                                                <strong>₹<span id="totalAmountDisplay"><?= number_format($invoice->total_amount, 2) ?></span></strong>
                                                <input type="hidden" name="total_amount" id="total_amount" value="<?= $invoice->total_amount ?>" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <a href="<?= url('/invoices/' . $invoice->id) ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <div>
                                <button type="submit" name="status" value="<?= $invoice->status ?>" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Invoice
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
let itemCounter = <?= count($items) ?: 1 ?>;

// Auto-fill client details when selected
document.getElementById('client_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    if (selectedOption.value) {
        document.getElementById('client_name').value = selectedOption.dataset.name || '';
        document.getElementById('client_email').value = selectedOption.dataset.email || '';
        document.getElementById('client_phone').value = selectedOption.dataset.phone || '';
        document.getElementById('client_address').value = selectedOption.dataset.address || '';
    }
});

// Add new invoice item
function addInvoiceItem() {
    const tbody = document.getElementById('invoiceItemsBody');
    const newRow = document.createElement('tr');
    newRow.className = 'invoice-item-row';
    newRow.innerHTML = `
        <td>
            <input type="text" name="items[${itemCounter}][description]"
                   class="form-control" placeholder="Item description" required>
        </td>
        <td>
            <input type="number" name="items[${itemCounter}][quantity]"
                   class="form-control quantity-input"
                   value="1" min="0.01" step="0.01" required>
        </td>
        <td>
            <input type="number" name="items[${itemCounter}][unit_price]"
                   class="form-control unit-price-input"
                   min="0.01" step="0.01" required>
        </td>
        <td>
            <input type="number" name="items[${itemCounter}][total_price]"
                   class="form-control total-price-input"
                   readonly>
        </td>
        <td>
            <button type="button" class="btn btn-danger btn-sm"
                    onclick="removeInvoiceItem(this)">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
    tbody.appendChild(newRow);
    itemCounter++;

    // Add event listeners to new inputs
    addCalculationListeners(newRow);
}

// Remove invoice item
function removeInvoiceItem(button) {
    const row = button.closest('tr');
    if (document.querySelectorAll('.invoice-item-row').length > 1) {
        row.remove();
        calculateTotals();
    } else {
        alert('At least one item is required.');
    }
}

// Add calculation event listeners
function addCalculationListeners(row) {
    const quantityInput = row.querySelector('.quantity-input');
    const unitPriceInput = row.querySelector('.unit-price-input');

    quantityInput.addEventListener('input', calculateRowTotal);
    unitPriceInput.addEventListener('input', calculateRowTotal);
}

// Calculate row total
function calculateRowTotal(event) {
    const row = event.target.closest('tr');
    const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
    const unitPrice = parseFloat(row.querySelector('.unit-price-input').value) || 0;
    const total = quantity * unitPrice;

    row.querySelector('.total-price-input').value = total.toFixed(2);
    calculateTotals();
}

// Calculate invoice totals
function calculateTotals() {
    let subtotal = 0;

    document.querySelectorAll('.total-price-input').forEach(input => {
        subtotal += parseFloat(input.value) || 0;
    });

    const discount = parseFloat(document.getElementById('discount_amount').value) || 0;
    const taxRate = parseFloat(document.getElementById('tax_rate').value) || 0;

    const taxableAmount = subtotal - discount;
    const taxAmount = taxableAmount * (taxRate / 100);
    const totalAmount = taxableAmount + taxAmount;

    document.getElementById('subtotalDisplay').textContent = subtotal.toFixed(2);
    document.getElementById('subtotal').value = subtotal.toFixed(2);

    document.getElementById('taxAmountDisplay').textContent = taxAmount.toFixed(2);
    document.getElementById('tax_amount').value = taxAmount.toFixed(2);

    document.getElementById('totalAmountDisplay').textContent = totalAmount.toFixed(2);
    document.getElementById('total_amount').value = totalAmount.toFixed(2);
}

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Add listeners to existing rows
    document.querySelectorAll('.invoice-item-row').forEach(addCalculationListeners);

    // Add listeners to discount and tax inputs
    document.getElementById('discount_amount').addEventListener('input', calculateTotals);
    document.getElementById('tax_rate').addEventListener('input', calculateTotals);

    // Initial calculation
    calculateTotals();
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_PATH . '/views/layouts/app.php';
?>
    <button type="submit" class="btn btn-primary">Update</button>
</form>
