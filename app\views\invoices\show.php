<?php
ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-print-none">
                    <h3 class="card-title">
                        <i class="fas fa-file-invoice"></i>
                        Invoice Details
                    </h3>
                    <div class="card-tools">
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary" onclick="window.print()">
                                <i class="fas fa-print"></i> Print
                            </button>
                            <a href="<?= url('/invoices/' . $invoice->id . '/print') ?>"
                               class="btn btn-secondary" target="_blank">
                                <i class="fas fa-external-link-alt"></i> Print View
                            </a>
                            <a href="<?= url('/invoices/' . $invoice->id . '/edit') ?>"
                               class="btn btn-warning">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            <button type="button" class="btn btn-danger"
                                    onclick="deleteInvoice(<?= $invoice->id ?>, '<?= e($invoice->invoice_number) ?>')">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                            <a href="<?= url('/invoices') ?>" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to List
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Invoice Header -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="invoice-company">
                                <h2 class="company-name"><?= e($invoice->company_name ?: 'Your Company Name') ?></h2>
                                <?php if ($invoice->company_address): ?>
                                    <div class="company-address">
                                        <?= nl2br(e($invoice->company_address)) ?>
                                    </div>
                                <?php endif; ?>
                                <div class="company-contact mt-2">
                                    <?php if ($invoice->company_phone): ?>
                                        <div><i class="fas fa-phone"></i> <?= e($invoice->company_phone) ?></div>
                                    <?php endif; ?>
                                    <?php if ($invoice->company_email): ?>
                                        <div><i class="fas fa-envelope"></i> <?= e($invoice->company_email) ?></div>
                                    <?php endif; ?>
                                    <?php if ($invoice->company_website): ?>
                                        <div><i class="fas fa-globe"></i> <?= e($invoice->company_website) ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 text-md-end">
                            <h1 class="invoice-title">INVOICE</h1>
                            <div class="invoice-meta">
                                <div class="mb-2">
                                    <strong>Invoice #:</strong> <?= e($invoice->invoice_number) ?>
                                </div>
                                <div class="mb-2">
                                    <strong>Date:</strong> <?= date('M j, Y', strtotime($invoice->invoice_date)) ?>
                                </div>
                                <?php if ($invoice->due_date): ?>
                                    <div class="mb-2">
                                        <strong>Due Date:</strong>
                                        <span class="<?= $invoice->isOverdue() ? 'text-danger' : '' ?>">
                                            <?= date('M j, Y', strtotime($invoice->due_date)) ?>
                                            <?php if ($invoice->isOverdue()): ?>
                                                <i class="fas fa-exclamation-triangle"></i>
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                                <div class="mb-2">
                                    <strong>Status:</strong>
                                    <span class="badge <?= $invoice->getStatusBadgeClass() ?>">
                                        <?= ucfirst($invoice->status) ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <!-- Bill To Section -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Bill To:</h5>
                            <div class="client-info">
                                <strong><?= e($invoice->client_name) ?></strong>
                                <?php if ($invoice->client_address): ?>
                                    <div class="mt-1"><?= nl2br(e($invoice->client_address)) ?></div>
                                <?php endif; ?>
                                <?php if ($invoice->client_phone): ?>
                                    <div class="mt-1"><i class="fas fa-phone"></i> <?= e($invoice->client_phone) ?></div>
                                <?php endif; ?>
                                <?php if ($invoice->client_email): ?>
                                    <div class="mt-1"><i class="fas fa-envelope"></i> <?= e($invoice->client_email) ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Invoice Items -->
                    <div class="table-responsive mb-4">
                        <table class="table table-bordered">
                            <thead class="table-dark">
                                <tr>
                                    <th>Description</th>
                                    <th width="15%" class="text-center">Quantity</th>
                                    <th width="20%" class="text-end">Unit Price</th>
                                    <th width="20%" class="text-end">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($items)): ?>
                                    <tr>
                                        <td colspan="4" class="text-center text-muted">No items found</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($items as $item): ?>
                                        <tr>
                                            <td><?= e($item->description) ?></td>
                                            <td class="text-center"><?= number_format($item->quantity, 2) ?></td>
                                            <td class="text-end"><?= $item->getFormattedUnitPrice() ?></td>
                                            <td class="text-end"><?= $item->getFormattedTotalPrice() ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Invoice Totals -->
                    <div class="row">
                        <div class="col-md-6">
                            <?php if ($invoice->notes): ?>
                                <div class="mb-3">
                                    <h6>Notes:</h6>
                                    <p><?= nl2br(e($invoice->notes)) ?></p>
                                </div>
                            <?php endif; ?>

                            <?php if ($invoice->terms): ?>
                                <div class="mb-3">
                                    <h6>Terms & Conditions:</h6>
                                    <p><?= nl2br(e($invoice->terms)) ?></p>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>Subtotal:</strong></td>
                                            <td class="text-end">₹<?= number_format($invoice->subtotal, 2) ?></td>
                                        </tr>
                                        <?php if ($invoice->discount_amount > 0): ?>
                                            <tr>
                                                <td><strong>Discount:</strong></td>
                                                <td class="text-end">-₹<?= number_format($invoice->discount_amount, 2) ?></td>
                                            </tr>
                                        <?php endif; ?>
                                        <?php if ($invoice->tax_rate > 0): ?>
                                            <tr>
                                                <td><strong>Tax (<?= number_format($invoice->tax_rate, 2) ?>%):</strong></td>
                                                <td class="text-end">₹<?= number_format($invoice->tax_amount, 2) ?></td>
                                            </tr>
                                        <?php endif; ?>
                                        <tr class="table-dark">
                                            <td><strong>Total Amount:</strong></td>
                                            <td class="text-end"><strong><?= $invoice->getFormattedTotal() ?></strong></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Status Update Section (for non-print view) -->
                    <div class="row mt-4 d-print-none">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title">Update Status</h6>
                                </div>
                                <div class="card-body">
                                    <form id="statusUpdateForm">
                                        <div class="input-group">
                                            <select name="status" id="statusSelect" class="form-control">
                                                <option value="draft" <?= $invoice->status === 'draft' ? 'selected' : '' ?>>Draft</option>
                                                <option value="sent" <?= $invoice->status === 'sent' ? 'selected' : '' ?>>Sent</option>
                                                <option value="paid" <?= $invoice->status === 'paid' ? 'selected' : '' ?>>Paid</option>
                                                <option value="overdue" <?= $invoice->status === 'overdue' ? 'selected' : '' ?>>Overdue</option>
                                                <option value="cancelled" <?= $invoice->status === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                                            </select>
                                            <button type="submit" class="btn btn-primary">Update</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title">Quick Actions</h6>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <button type="button" class="btn btn-success" onclick="markAsPaid()">
                                            <i class="fas fa-check"></i> Mark as Paid
                                        </button>
                                        <button type="button" class="btn btn-info" onclick="sendInvoice()">
                                            <i class="fas fa-paper-plane"></i> Send to Client
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete invoice <strong id="invoiceNumber"></strong>?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">Delete Invoice</button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .d-print-none {
        display: none !important;
    }

    .invoice-title {
        font-size: 2.5rem;
        color: #333;
    }

    .company-name {
        font-size: 1.8rem;
        color: #333;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .table-bordered {
        border: 2px solid #000 !important;
    }

    .table-bordered th,
    .table-bordered td {
        border: 1px solid #000 !important;
    }

    .table-dark {
        background-color: #333 !important;
        color: white !important;
    }

    body {
        background: white !important;
    }
}
</style>

<script>
// Delete invoice function
function deleteInvoice(id, invoiceNumber) {
    document.getElementById('invoiceNumber').textContent = invoiceNumber;
    document.getElementById('deleteForm').action = '<?= url('/invoices/') ?>' + id;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// Update status
document.getElementById('statusUpdateForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const status = document.getElementById('statusSelect').value;
    const formData = new FormData();
    formData.append('status', status);

    fetch('<?= url('/invoices/' . $invoice->id . '/status') ?>', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating status: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error updating status: ' + error.message);
    });
});

// Mark as paid
function markAsPaid() {
    if (confirm('Mark this invoice as paid?')) {
        document.getElementById('statusSelect').value = 'paid';
        document.getElementById('statusUpdateForm').dispatchEvent(new Event('submit'));
    }
}

// Send invoice (placeholder)
function sendInvoice() {
    alert('Email functionality would be implemented here. For now, you can print and send manually.');
}
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_PATH . '/views/layouts/app.php';
?>
