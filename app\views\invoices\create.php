<?php
ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-plus-circle"></i>
                        Create New Invoice
                    </h3>
                    <div class="card-tools">
                        <a href="<?= url('/invoices') ?>" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Invoices
                        </a>
                    </div>
                </div>

                <form id="invoiceForm" action="<?= url('/invoices') ?>" method="POST">
                    <div class="card-body">
                        <!-- Compact Form Layout -->
                        <div class="row mb-3">
                            <!-- Left Column -->
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header py-2">
                                        <h6 class="mb-0"><i class="fas fa-file-invoice"></i> Invoice Details</h6>
                                    </div>
                                    <div class="card-body py-2">
                                        <div class="mb-2">
                                            <label for="invoice_number" class="form-label form-label-sm">Invoice Number</label>
                                            <input type="text" name="invoice_number" id="invoice_number"
                                                   class="form-control form-control-sm" value="<?= e($invoiceNumber) ?>" readonly>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6 mb-2">
                                                <label for="invoice_date" class="form-label form-label-sm">Invoice Date</label>
                                                <input type="date" name="invoice_date" id="invoice_date"
                                                       class="form-control form-control-sm" value="<?= date('Y-m-d') ?>" required>
                                            </div>
                                            <div class="col-md-6 mb-2">
                                                <label for="due_date" class="form-label form-label-sm">Due Date</label>
                                                <input type="date" name="due_date" id="due_date"
                                                       class="form-control form-control-sm" value="<?= date('Y-m-d', strtotime('+30 days')) ?>">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card">
                                    <div class="card-header py-2">
                                        <h6 class="mb-0"><i class="fas fa-user"></i> Bill To</h6>
                                    </div>
                                    <div class="card-body py-2">
                                        <div class="mb-2">
                                            <label for="client_id" class="form-label form-label-sm">Select Client</label>
                                            <select name="client_id" id="client_id" class="form-control form-control-sm">
                                                <option value="">Select existing client or enter manually</option>
                                                <?php if (isset($clients) && !empty($clients)): ?>
                                                    <?php foreach ($clients as $client): ?>
                                                        <option value="<?= e($client['id']) ?>"
                                                                data-name="<?= e($client['name']) ?>"
                                                                data-email="<?= e($client['email']) ?>"
                                                                data-phone="<?= e($client['phone']) ?>"
                                                                data-address="<?= e($client['address']) ?>">
                                                            <?= e($client['name']) ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                <?php endif; ?>
                                            </select>
                                        </div>
                                        <div class="mb-2">
                                            <label for="client_name" class="form-label form-label-sm">Client Name *</label>
                                            <input type="text" name="client_name" id="client_name"
                                                   class="form-control form-control-sm" required>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6 mb-2">
                                                <label for="client_email" class="form-label form-label-sm">Email</label>
                                                <input type="email" name="client_email" id="client_email"
                                                       class="form-control form-control-sm">
                                            </div>
                                            <div class="col-md-6 mb-2">
                                                <label for="client_phone" class="form-label form-label-sm">Phone</label>
                                                <input type="text" name="client_phone" id="client_phone"
                                                       class="form-control form-control-sm">
                                            </div>
                                        </div>
                                        <div class="mb-2">
                                            <label for="client_address" class="form-label form-label-sm">Address</label>
                                            <textarea name="client_address" id="client_address"
                                                      class="form-control form-control-sm" rows="2"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column -->
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header py-2">
                                        <h6 class="mb-0"><i class="fas fa-building"></i> Company Information</h6>
                                    </div>
                                    <div class="card-body py-2">
                                        <div class="mb-2">
                                            <label for="company_name" class="form-label form-label-sm">Company Name</label>
                                            <input type="text" name="company_name" id="company_name"
                                                   class="form-control form-control-sm" value="Nice Techs">
                                        </div>
                                        <div class="mb-2">
                                            <label for="company_address" class="form-label form-label-sm">Address</label>
                                            <textarea name="company_address" id="company_address"
                                                      class="form-control form-control-sm" rows="2">Khirni Bagh, Shahjahanpur</textarea>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6 mb-2">
                                                <label for="company_phone" class="form-label form-label-sm">Phone</label>
                                                <input type="text" name="company_phone" id="company_phone"
                                                       class="form-control form-control-sm" value="+91-8573061818">
                                            </div>
                                            <div class="col-md-6 mb-2">
                                                <label for="company_email" class="form-label form-label-sm">Email</label>
                                                <input type="email" name="company_email" id="company_email"
                                                       class="form-control form-control-sm" value="<EMAIL>">
                                            </div>
                                        </div>
                                    </div>
                                </div>


                            </div>
                        </div>

                        <!-- Invoice Items -->
                        <div class="card mb-3">
                            <div class="card-header py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0"><i class="fas fa-list"></i> Invoice Items</h6>
                                    <button type="button" class="btn btn-success btn-sm" onclick="addInvoiceItem()">
                                        <i class="fas fa-plus"></i> Add Item
                                    </button>
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered mb-0" id="invoiceItemsTable">
                                        <thead class="table-light">
                                            <tr>
                                                <th width="40%">Description</th>
                                                <th width="15%">Qty</th>
                                                <th width="20%">Unit Price (₹)</th>
                                                <th width="20%">Total (₹)</th>
                                                <th width="5%">Action</th>
                                            </tr>
                                        </thead>
                                        <tbody id="invoiceItemsBody">
                                            <tr class="invoice-item-row">
                                                <td>
                                                    <input type="text" name="items[0][description]"
                                                           class="form-control form-control-sm" placeholder="Item description" required>
                                                </td>
                                                <td>
                                                    <input type="number" name="items[0][quantity]"
                                                           class="form-control form-control-sm quantity-input"
                                                           value="1" min="0.01" step="0.01" required>
                                                </td>
                                                <td>
                                                    <input type="number" name="items[0][unit_price]"
                                                           class="form-control form-control-sm unit-price-input"
                                                           min="0.01" step="0.01" required>
                                                </td>
                                                <td>
                                                    <input type="number" name="items[0][total_price]"
                                                           class="form-control form-control-sm total-price-input"
                                                           readonly>
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-danger btn-sm"
                                                            onclick="removeInvoiceItem(this)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Information -->
                        <div class="card mb-3">
                            <div class="card-header py-2">
                                <h6 class="mb-0"><i class="fas fa-credit-card"></i> Payment Information</h6>
                            </div>
                            <div class="card-body py-2">
                                <div class="row">
                                    <div class="col-md-3 mb-2">
                                        <label for="paid_amount" class="form-label form-label-sm">Paid Amount (₹)</label>
                                        <input type="number" name="paid_amount" id="paid_amount"
                                               class="form-control form-control-sm" value="0" min="0" step="0.01">
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <label class="form-label form-label-sm">Remaining Amount</label>
                                        <div class="form-control form-control-sm bg-light" id="remaining_amount_display">₹0.00</div>
                                        <input type="hidden" name="remaining_amount" id="remaining_amount" value="0">
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <label for="payment_method" class="form-label form-label-sm">Payment Method</label>
                                        <select name="payment_method" id="payment_method" class="form-control form-control-sm">
                                            <option value="">Select Method</option>
                                            <option value="cash">Cash</option>
                                            <option value="bank_transfer">Bank Transfer</option>
                                            <option value="upi">UPI</option>
                                            <option value="cheque">Cheque</option>
                                            <option value="card">Card</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <label for="payment_date" class="form-label form-label-sm">Payment Date</label>
                                        <input type="date" name="payment_date" id="payment_date"
                                               class="form-control form-control-sm">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Invoice Totals -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header py-2">
                                        <h6 class="mb-0"><i class="fas fa-sticky-note"></i> Notes & Terms</h6>
                                    </div>
                                    <div class="card-body py-2">
                                        <div class="mb-2">
                                            <label for="notes" class="form-label form-label-sm">Notes</label>
                                            <textarea name="notes" id="notes" class="form-control form-control-sm" rows="2"
                                                      placeholder="Additional notes or comments"></textarea>
                                        </div>
                                        <div class="mb-2">
                                            <label for="terms" class="form-label form-label-sm">Terms & Conditions</label>
                                            <textarea name="terms" id="terms" class="form-control form-control-sm" rows="2"
                                                      placeholder="Payment terms and conditions">Payment due within 30 days of invoice date.</textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header py-2">
                                        <h6 class="mb-0"><i class="fas fa-calculator"></i> Invoice Summary</h6>
                                    </div>
                                    <div class="card-body py-2">
                                        <div class="row mb-1 align-items-center">
                                            <div class="col-6"><small>Subtotal:</small></div>
                                            <div class="col-6 text-end">
                                                <small>₹<span id="subtotalDisplay">0.00</span></small>
                                                <input type="hidden" name="subtotal" id="subtotal" value="0">
                                            </div>
                                        </div>

                                        <div class="row mb-1 align-items-center">
                                            <div class="col-6">
                                                <label for="discount_amount" class="form-label form-label-sm mb-0">Discount (₹):</label>
                                            </div>
                                            <div class="col-6">
                                                <input type="number" name="discount_amount" id="discount_amount"
                                                       class="form-control form-control-sm" value="0" min="0" step="0.01">
                                            </div>
                                        </div>

                                        <div class="row mb-1 align-items-center">
                                            <div class="col-6">
                                                <label for="tax_rate" class="form-label form-label-sm mb-0">Tax Rate (%):</label>
                                            </div>
                                            <div class="col-6">
                                                <input type="number" name="tax_rate" id="tax_rate"
                                                       class="form-control form-control-sm" value="18" min="0" max="100" step="0.01">
                                            </div>
                                        </div>

                                        <div class="row mb-1 align-items-center">
                                            <div class="col-6"><small>Tax Amount:</small></div>
                                            <div class="col-6 text-end">
                                                <small>₹<span id="taxAmountDisplay">0.00</span></small>
                                                <input type="hidden" name="tax_amount" id="tax_amount" value="0">
                                            </div>
                                        </div>

                                        <hr class="my-2">

                                        <div class="row align-items-center">
                                            <div class="col-6"><strong>Total Amount:</strong></div>
                                            <div class="col-6 text-end">
                                                <strong>₹<span id="totalAmountDisplay">0.00</span></strong>
                                                <input type="hidden" name="total_amount" id="total_amount" value="0" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <a href="<?= url('/invoices') ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <div>
                                <button type="submit" name="status" value="draft" class="btn btn-warning">
                                    <i class="fas fa-save"></i> Save as Draft
                                </button>
                                <button type="submit" name="status" value="sent" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i> Create & Send
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
let itemCounter = 1;

// Auto-fill client details when selected
document.getElementById('client_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    if (selectedOption.value) {
        document.getElementById('client_name').value = selectedOption.dataset.name || '';
        document.getElementById('client_email').value = selectedOption.dataset.email || '';
        document.getElementById('client_phone').value = selectedOption.dataset.phone || '';
        document.getElementById('client_address').value = selectedOption.dataset.address || '';
    }
});

// Add new invoice item
function addInvoiceItem() {
    const tbody = document.getElementById('invoiceItemsBody');
    const newRow = document.createElement('tr');
    newRow.className = 'invoice-item-row';
    newRow.innerHTML = `
        <td>
            <input type="text" name="items[${itemCounter}][description]"
                   class="form-control form-control-sm" placeholder="Item description" required>
        </td>
        <td>
            <input type="number" name="items[${itemCounter}][quantity]"
                   class="form-control form-control-sm quantity-input"
                   value="1" min="0.01" step="0.01" required>
        </td>
        <td>
            <input type="number" name="items[${itemCounter}][unit_price]"
                   class="form-control form-control-sm unit-price-input"
                   min="0.01" step="0.01" required>
        </td>
        <td>
            <input type="number" name="items[${itemCounter}][total_price]"
                   class="form-control form-control-sm total-price-input"
                   readonly>
        </td>
        <td>
            <button type="button" class="btn btn-danger btn-sm"
                    onclick="removeInvoiceItem(this)">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
    tbody.appendChild(newRow);
    itemCounter++;

    // Add event listeners to new inputs
    addCalculationListeners(newRow);
}

// Remove invoice item
function removeInvoiceItem(button) {
    const row = button.closest('tr');
    if (document.querySelectorAll('.invoice-item-row').length > 1) {
        row.remove();
        calculateTotals();
    } else {
        alert('At least one item is required.');
    }
}

// Add calculation event listeners
function addCalculationListeners(row) {
    const quantityInput = row.querySelector('.quantity-input');
    const unitPriceInput = row.querySelector('.unit-price-input');

    quantityInput.addEventListener('input', calculateRowTotal);
    unitPriceInput.addEventListener('input', calculateRowTotal);
}

// Calculate row total
function calculateRowTotal(event) {
    const row = event.target.closest('tr');
    const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
    const unitPrice = parseFloat(row.querySelector('.unit-price-input').value) || 0;
    const total = quantity * unitPrice;

    row.querySelector('.total-price-input').value = total.toFixed(2);
    calculateTotals();
}

// Calculate invoice totals
function calculateTotals() {
    let subtotal = 0;

    document.querySelectorAll('.total-price-input').forEach(input => {
        subtotal += parseFloat(input.value) || 0;
    });

    const discount = parseFloat(document.getElementById('discount_amount').value) || 0;
    const taxRate = parseFloat(document.getElementById('tax_rate').value) || 0;

    const taxableAmount = subtotal - discount;
    const taxAmount = taxableAmount * (taxRate / 100);
    const totalAmount = taxableAmount + taxAmount;

    document.getElementById('subtotalDisplay').textContent = subtotal.toFixed(2);
    document.getElementById('subtotal').value = subtotal.toFixed(2);

    document.getElementById('taxAmountDisplay').textContent = taxAmount.toFixed(2);
    document.getElementById('tax_amount').value = taxAmount.toFixed(2);

    document.getElementById('totalAmountDisplay').textContent = totalAmount.toFixed(2);
    document.getElementById('total_amount').value = totalAmount.toFixed(2);

    // Calculate remaining amount
    calculateRemainingAmount(totalAmount);
}

// Calculate remaining amount
function calculateRemainingAmount(totalAmount = null) {
    if (totalAmount === null) {
        totalAmount = parseFloat(document.getElementById('total_amount').value) || 0;
    }

    const paidAmount = parseFloat(document.getElementById('paid_amount').value) || 0;
    const remainingAmount = Math.max(0, totalAmount - paidAmount); // Ensure non-negative

    const remainingDisplay = document.getElementById('remaining_amount_display');
    const remainingInput = document.getElementById('remaining_amount');

    if (remainingDisplay) {
        remainingDisplay.textContent = '₹' + remainingAmount.toFixed(2);
    }
    if (remainingInput) {
        remainingInput.value = remainingAmount.toFixed(2);
    }
}

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Add listeners to existing rows
    document.querySelectorAll('.invoice-item-row').forEach(addCalculationListeners);

    // Add listeners to discount and tax inputs
    document.getElementById('discount_amount').addEventListener('input', calculateTotals);
    document.getElementById('tax_rate').addEventListener('input', calculateTotals);

    // Add listener to paid amount input
    const paidAmountInput = document.getElementById('paid_amount');
    if (paidAmountInput) {
        paidAmountInput.addEventListener('input', function() {
            calculateRemainingAmount();
        });
    }

    // Initial calculation
    calculateTotals();

    // Initial remaining amount calculation
    setTimeout(function() {
        calculateRemainingAmount();
    }, 100);
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_PATH . '/views/layouts/app.php';
?>
