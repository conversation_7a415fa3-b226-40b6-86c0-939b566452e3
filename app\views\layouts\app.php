<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? e($title) . ' - ' : '' ?><?= e($config['app']['name']) ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?= asset('css/app.css') ?>" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= asset('favicon.ico') ?>">
    
    <meta name="csrf-token" content="<?= csrf_token() ?>">
</head>
<body class="<?= isset($bodyClass) ? e($bodyClass) : '' ?>">
    
    <?php if (isset($isLoggedIn) && $isLoggedIn): ?>
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <!-- Brand -->
            <div class="sidebar-brand">
                <a href="<?= url('/dashboard') ?>" class="brand-link">
                    <i class="fas fa-server me-2"></i>
                    <span class="brand-text"><?= e($config['app']['name']) ?></span>
                </a>
            </div>

            <!-- User Info -->
            <div class="sidebar-user">
                <div class="user-panel">
                    <div class="user-image">
                        <?= avatar($user, 40) ?>
                    </div>
                    <div class="user-info">
                        <span class="user-name"><?= e($user['first_name'] . ' ' . $user['last_name']) ?></span>
                        <small class="user-role"><?= e($user['role']) ?></small>
                    </div>
                </div>
            </div>

            <!-- Navigation Menu -->
            <nav class="sidebar-nav">
                <ul class="nav nav-pills nav-sidebar flex-column">
                    <li class="nav-item">
                        <a class="nav-link <?= active_menu('/dashboard') ?>" href="<?= url('/dashboard') ?>">
                            <i class="fas fa-tachometer-alt nav-icon"></i>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= active_menu('/clients') ?>" href="<?= url('/clients') ?>">
                            <i class="fas fa-users nav-icon"></i>
                            <span class="nav-text">Clients</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= active_menu('/domains') ?>" href="<?= url('/domains') ?>">
                            <i class="fas fa-globe nav-icon"></i>
                            <span class="nav-text">Domains</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= active_menu('/servers') ?>" href="<?= url('/servers') ?>">
                            <i class="fas fa-server nav-icon"></i>
                            <span class="nav-text">Servers</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= active_menu('/mediators') ?>" href="<?= url('/mediators') ?>">
                            <i class="fas fa-handshake nav-icon"></i>
                            <span class="nav-text">Vendor</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= active_menu('/reports') ?>" href="<?= url('/reports') ?>">
                            <i class="fas fa-chart-bar nav-icon"></i>
                            <span class="nav-text">Reports</span>
                        </a>
                    </li>

<!-- Notifications -->
<li class="nav-item">
    <a class="nav-link position-relative" href="<?= url('/notifications') ?>">
        <i class="fas fa-bell nav-icon"></i>
        <span class="nav-text">Notifications</span>
        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="notification-count" style="display: none;">
            0
        </span>
    </a>
</li>
<!-- Invoice -->
<li class="nav-item">
    <a class="nav-link <?= active_menu('/invoices') ?>" href="<?= url('/invoices') ?>">
        <i class="fas fa-file-invoice nav-icon"></i>
        <span class="nav-text">Invoice</span>
    </a>
</li>

                    <?php if (can('admin')): ?>
                    <!-- Admin Section -->
                    <li class="nav-header">ADMINISTRATION</li>
                    <li class="nav-item">
                        <a class="nav-link <?= active_menu('/users') ?>" href="<?= url('/users') ?>">
                            <i class="fas fa-user-cog nav-icon"></i>
                            <span class="nav-text">Users</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= active_menu('/settings') ?>" href="<?= url('/settings') ?>">
                            <i class="fas fa-cogs nav-icon"></i>
                            <span class="nav-text">Settings</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= active_menu('/backup') ?>" href="<?= url('/backup') ?>">
                            <i class="fas fa-download nav-icon"></i>
                            <span class="nav-text">Backup</span>
                        </a>
                    </li>
                    <?php endif; ?>

                    <!-- User Actions -->
                    <li class="nav-header">ACCOUNT</li>
                    <li class="nav-item">
                        <a class="nav-link <?= active_menu('/profile') ?>" href="<?= url('/profile') ?>">
                            <i class="fas fa-user nav-icon"></i>
                            <span class="nav-text">Profile</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= url('/logout') ?>">
                            <i class="fas fa-sign-out-alt nav-icon"></i>
                            <span class="nav-text">Logout</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>

        <!-- Mobile Header -->
        <div class="mobile-header d-lg-none">
            <button class="btn btn-link sidebar-toggle" id="sidebar-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <span class="mobile-brand"><?= e($config['app']['name']) ?></span>
        </div>

        <!-- Sidebar Overlay -->
        <div class="sidebar-overlay" id="sidebar-overlay"></div>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="<?= isset($isLoggedIn) && $isLoggedIn ? 'main-content' : '' ?>">
        <!-- Flash Messages -->
        <?php $flashMessages = flash_messages(); ?>
        <?php if (!empty($flashMessages)): ?>
            <div class="row">
                <div class="col-12">
                    <?php foreach ($flashMessages as $message): ?>
                        <div class="alert alert-<?= $message['type'] === 'error' ? 'danger' : $message['type'] ?> alert-dismissible fade show" role="alert">
                            <?= e($message['message']) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Page Content -->
        <?= $content ?>
    </main>
    
    <!-- Footer -->
    <?php if (isset($isLoggedIn) && $isLoggedIn): ?>
    <footer class="main-footer">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <small class="text-muted">
                        &copy; <?= date('Y') ?> <?= e($config['app']['name']) ?>. All rights reserved.
                    </small>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        Version <?= e($config['app']['version']) ?> |
                        <a href="#" class="text-decoration-none">Support</a>
                    </small>
                </div>
            </div>
        </div>
    </footer>
    <?php endif; ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?= asset('js/app.js') ?>"></script>
    
    <!-- Page-specific scripts -->
    <?php if (isset($scripts)): ?>
        <?php foreach ($scripts as $script): ?>
            <script src="<?= asset($script) ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <script>
        // Global JavaScript variables
        window.appConfig = {
            baseUrl: '<?= url() ?>',
            csrfToken: '<?= csrf_token() ?>',
            user: <?= json_encode($user ?? null) ?>
        };
        
        // Load notifications on page load
        $(document).ready(function() {
            loadNotifications();
            
            // Refresh notifications every 5 minutes
            setInterval(loadNotifications, 300000);
        });
        
        function loadNotifications() {
            $.get('<?= url('/api/notifications') ?>')
                .done(function(data) {
                    if (data.success) {
                        updateNotificationBadge(data.unread_count);
                        updateNotificationList(data.notifications);
                    }
                });
        }
        
        function updateNotificationBadge(count) {
            const badge = $('#notification-count');
            if (count > 0) {
                badge.text(count).show();
            } else {
                badge.hide();
            }
        }

        function updateNotificationList(notifications) {
            // This function is kept for compatibility but notifications are now handled on the notifications page
            // The badge update is sufficient for the sidebar
        }
        
        function markAsRead(notificationId) {
            $.post('<?= url('/api/notifications/mark-read') ?>', {
                id: notificationId,
                _token: '<?= csrf_token() ?>'
            }).done(function() {
                loadNotifications();
            });
        }
    </script>
</body>
</html>
