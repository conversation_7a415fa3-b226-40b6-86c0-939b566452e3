<?php
ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-file-invoice"></i>
                        Invoice Management
                    </h3>
                    <div class="card-tools">
                        <a href="<?= url('/invoices/create') ?>" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create New Invoice
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Quick Stats -->
                    <div class="row mb-4">
                        <?php
                        $totalInvoices = count($invoices);
                        $paidInvoices = array_filter($invoices, function($inv) { return $inv->status === 'paid'; });
                        $overdueInvoices = array_filter($invoices, function($inv) { return $inv->status === 'overdue'; });
                        $totalAmount = array_sum(array_map(function($inv) { return $inv->total_amount; }, $invoices));
                        ?>

                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-info">
                                <div class="inner">
                                    <h3><?= $totalInvoices ?></h3>
                                    <p>Total Invoices</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-file-invoice"></i>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-success">
                                <div class="inner">
                                    <h3><?= count($paidInvoices) ?></h3>
                                    <p>Paid Invoices</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-danger">
                                <div class="inner">
                                    <h3><?= count($overdueInvoices) ?></h3>
                                    <p>Overdue Invoices</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-warning">
                                <div class="inner">
                                    <h3>₹<?= number_format($totalAmount, 2) ?></h3>
                                    <p>Total Amount</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-rupee-sign"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <select id="statusFilter" class="form-control">
                                <option value="">All Status</option>
                                <option value="draft">Draft</option>
                                <option value="sent">Sent</option>
                                <option value="paid">Paid</option>
                                <option value="overdue">Overdue</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="text" id="clientFilter" class="form-control" placeholder="Search by client...">
                        </div>
                        <div class="col-md-3">
                            <input type="date" id="dateFromFilter" class="form-control" placeholder="From date">
                        </div>
                        <div class="col-md-3">
                            <input type="date" id="dateToFilter" class="form-control" placeholder="To date">
                        </div>
                    </div>

                    <!-- Invoices Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="invoicesTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>Invoice #</th>
                                    <th>Client</th>
                                    <th>Date</th>
                                    <th>Due Date</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($invoices)): ?>
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-file-invoice fa-3x mb-3"></i>
                                                <p>No invoices found. <a href="<?= url('/invoices/create') ?>">Create your first invoice</a></p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($invoices as $invoice): ?>
                                        <tr data-status="<?= e($invoice->status) ?>"
                                            data-client="<?= e(strtolower($invoice->client_name)) ?>"
                                            data-date="<?= e($invoice->invoice_date) ?>">
                                            <td>
                                                <strong><?= e($invoice->invoice_number) ?></strong>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?= e($invoice->client_name) ?></strong>
                                                    <?php if ($invoice->client_email): ?>
                                                        <br><small class="text-muted"><?= e($invoice->client_email) ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td><?= date('M j, Y', strtotime($invoice->invoice_date)) ?></td>
                                            <td>
                                                <?php if ($invoice->due_date): ?>
                                                    <?= date('M j, Y', strtotime($invoice->due_date)) ?>
                                                    <?php if ($invoice->isOverdue()): ?>
                                                        <br><small class="text-danger">
                                                            <i class="fas fa-exclamation-triangle"></i> Overdue
                                                        </small>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong><?= $invoice->getFormattedTotal() ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge <?= $invoice->getStatusBadgeClass() ?>">
                                                    <?= ucfirst($invoice->status) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= url('/invoices/' . $invoice->id) ?>"
                                                       class="btn btn-info btn-sm" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?= url('/invoices/' . $invoice->id . '/print') ?>"
                                                       class="btn btn-secondary btn-sm" title="Print" target="_blank">
                                                        <i class="fas fa-print"></i>
                                                    </a>
                                                    <a href="<?= url('/invoices/' . $invoice->id . '/edit') ?>"
                                                       class="btn btn-warning btn-sm" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-danger btn-sm"
                                                            title="Delete" onclick="deleteInvoice(<?= $invoice->id ?>, '<?= e($invoice->invoice_number) ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete invoice <strong id="invoiceNumber"></strong>?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">Delete Invoice</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Delete invoice function
function deleteInvoice(id, invoiceNumber) {
    document.getElementById('invoiceNumber').textContent = invoiceNumber;
    document.getElementById('deleteForm').action = '<?= url('/invoices/') ?>' + id;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// Filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const statusFilter = document.getElementById('statusFilter');
    const clientFilter = document.getElementById('clientFilter');
    const dateFromFilter = document.getElementById('dateFromFilter');
    const dateToFilter = document.getElementById('dateToFilter');
    const tableRows = document.querySelectorAll('#invoicesTable tbody tr[data-status]');

    function filterTable() {
        const statusValue = statusFilter.value.toLowerCase();
        const clientValue = clientFilter.value.toLowerCase();
        const dateFromValue = dateFromFilter.value;
        const dateToValue = dateToFilter.value;

        tableRows.forEach(row => {
            const rowStatus = row.dataset.status;
            const rowClient = row.dataset.client;
            const rowDate = row.dataset.date;

            let showRow = true;

            // Status filter
            if (statusValue && rowStatus !== statusValue) {
                showRow = false;
            }

            // Client filter
            if (clientValue && !rowClient.includes(clientValue)) {
                showRow = false;
            }

            // Date range filter
            if (dateFromValue && rowDate < dateFromValue) {
                showRow = false;
            }
            if (dateToValue && rowDate > dateToValue) {
                showRow = false;
            }

            row.style.display = showRow ? '' : 'none';
        });

        // Update visible count
        const visibleRows = Array.from(tableRows).filter(row => row.style.display !== 'none');
        updateResultsCount(visibleRows.length, tableRows.length);
    }

    function updateResultsCount(visible, total) {
        let countElement = document.getElementById('resultsCount');
        if (!countElement) {
            countElement = document.createElement('div');
            countElement.id = 'resultsCount';
            countElement.className = 'text-muted mb-2';
            document.querySelector('.table-responsive').insertBefore(countElement, document.querySelector('#invoicesTable'));
        }
        countElement.textContent = `Showing ${visible} of ${total} invoices`;
    }

    // Add event listeners
    statusFilter.addEventListener('change', filterTable);
    clientFilter.addEventListener('input', filterTable);
    dateFromFilter.addEventListener('change', filterTable);
    dateToFilter.addEventListener('change', filterTable);

    // Initial count
    updateResultsCount(tableRows.length, tableRows.length);
});

// Auto-refresh overdue status
setInterval(function() {
    // Check for overdue invoices and update status
    const today = new Date().toISOString().split('T')[0];
    document.querySelectorAll('#invoicesTable tbody tr[data-status]').forEach(row => {
        const dueDate = row.querySelector('td:nth-child(4)').textContent;
        const status = row.dataset.status;

        if (status !== 'paid' && status !== 'cancelled' && dueDate && dueDate < today) {
            // Mark as overdue visually (you might want to implement AJAX update here)
            const statusBadge = row.querySelector('.badge');
            if (statusBadge && !statusBadge.classList.contains('bg-danger')) {
                statusBadge.className = 'badge bg-danger';
                statusBadge.textContent = 'Overdue';
                row.dataset.status = 'overdue';
            }
        }
    });
}, 60000); // Check every minute
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_PATH . '/views/layouts/app.php';
?>
