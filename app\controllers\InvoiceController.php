<?php

class InvoiceController extends Controller
{
    // Display a list of invoices
    public function index()
    {
        $invoices = Invoice::all(); // Assuming Invoice model exists
        View::render('invoices/index', ['invoices' => $invoices]);
    }

    // Show the form to create a new invoice
    public function create()
    {
        View::render('invoices/create');
    }

    // Store a new invoice
    public function store()
    {
        $data = $_POST;
        $invoice = new Invoice($data);
        $invoice->save();
        redirect('/invoices');
    }

    // Display a specific invoice
    public function show($id)
    {
        $invoice = Invoice::find($id);
        View::render('invoices/show', ['invoice' => $invoice]);
    }

    // Show the form to edit an invoice
    public function edit($id)
    {
        $invoice = Invoice::find($id);
        View::render('invoices/edit', ['invoice' => $invoice]);
    }

    // Update an invoice
    public function update($id)
    {
        $data = $_POST;
        $invoice = Invoice::find($id);
        $invoice->update($data);
        redirect('/invoices');
    }

    // Delete an invoice
    public function delete($id)
    {
        $invoice = Invoice::find($id);
        $invoice->delete();
        redirect('/invoices');
    }
}
