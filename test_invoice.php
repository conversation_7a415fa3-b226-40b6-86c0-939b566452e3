<?php
// Define constants
define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('PUBLIC_PATH', ROOT_PATH . '/public');
define('CACHE_PATH', ROOT_PATH . '/cache');
define('DATABASE_PATH', ROOT_PATH . '/database');

// Environment detection
define('IS_LOCAL', true);
define('DEBUG_MODE', true);

require_once 'app/core/Database.php';
require_once 'config/config.php';
require_once 'config/database.php';

try {
    $db = Database::getInstance()->getConnection();
    
    // Check if invoices table exists
    $stmt = $db->query("SHOW TABLES LIKE 'invoices'");
    $result = $stmt->fetch();
    
    if ($result) {
        echo "✅ Invoice table exists\n";
        
        // Check table structure
        $stmt = $db->query("DESCRIBE invoices");
        $columns = $stmt->fetchAll();
        echo "Columns:\n";
        foreach ($columns as $col) {
            echo "- " . $col['Field'] . " (" . $col['Type'] . ")\n";
        }
    } else {
        echo "❌ Invoice table does not exist\n";
    }
    
    // Check if invoice_items table exists
    $stmt = $db->query("SHOW TABLES LIKE 'invoice_items'");
    $result = $stmt->fetch();
    
    if ($result) {
        echo "\n✅ Invoice items table exists\n";
    } else {
        echo "\n❌ Invoice items table does not exist\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
