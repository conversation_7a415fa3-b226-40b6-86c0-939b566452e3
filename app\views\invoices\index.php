<h1>Invoices</h1>

<a href="<?= url('/invoices/create') ?>" class="btn btn-primary">Create Invoice</a>

<table class="table table-striped">
    <thead>
        <tr>
            <th>ID</th>
            <th>Client</th>
            <th>Amount</th>
            <th>Date</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        <?php foreach ($invoices as $invoice): ?>
            <tr>
                <td><?= e($invoice->id) ?></td>
                <td><?= e($invoice->client_name) ?></td>
                <td><?= e($invoice->amount) ?></td>
                <td><?= e($invoice->date) ?></td>
                <td>
                    <a href="<?= url('/invoices/' . $invoice->id) ?>" class="btn btn-info btn-sm">View</a>
                    <a href="<?= url('/invoices/' . $invoice->id . '/edit') ?>" class="btn btn-warning btn-sm">Edit</a>
                    <form action="<?= url('/invoices/' . $invoice->id) ?>" method="POST" style="display:inline;">
                        <input type="hidden" name="_method" value="DELETE">
                        <button type="submit" class="btn btn-danger btn-sm">Delete</button>
                    </form>
                </td>
            </tr>
        <?php endforeach; ?>
    </tbody>
</table>
