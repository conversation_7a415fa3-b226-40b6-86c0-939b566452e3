<?php

class InvoiceItem extends Model
{
    protected $table = 'invoice_items';
    protected $fillable = ['invoice_id', 'description', 'quantity', 'unit_price', 'total_price'];

    // Relationship with invoice
    public function invoice()
    {
        return $this->belongsTo('Invoice', 'invoice_id');
    }

    // Calculate total price
    public function calculateTotal()
    {
        $this->total_price = $this->quantity * $this->unit_price;
        return $this;
    }

    // Get formatted unit price
    public function getFormattedUnitPrice()
    {
        return '₹' . number_format($this->unit_price, 2);
    }

    // Get formatted total price
    public function getFormattedTotalPrice()
    {
        return '₹' . number_format($this->total_price, 2);
    }
}
