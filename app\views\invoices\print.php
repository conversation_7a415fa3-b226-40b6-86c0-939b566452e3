<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice <?= e($invoice->invoice_number) ?> - Print</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: white;
            color: #333;
            font-size: 14px;
            line-height: 1.4;
        }

        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 15px;
        }

        .invoice-header {
            border-bottom: 2px solid #007bff;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }

        .company-name {
            font-size: 1.8rem;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }

        .invoice-title {
            font-size: 2.2rem;
            font-weight: bold;
            color: #007bff;
            text-align: right;
            margin-bottom: 10px;
        }

        .invoice-meta {
            font-size: 0.95rem;
        }

        .client-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }

        .items-table {
            margin-bottom: 20px;
            font-size: 0.9rem;
        }

        .items-table th {
            background-color: #007bff;
            color: white;
            padding: 10px 8px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .items-table td {
            padding: 8px;
            border-bottom: 1px solid #ddd;
            vertical-align: middle;
        }

        .totals-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }

        .total-row {
            font-size: 1.1rem;
            font-weight: bold;
            background-color: #007bff !important;
            color: white !important;
        }

        .payment-info {
            background-color: #e7f3ff;
            padding: 10px;
            border-radius: 5px;
            margin-top: 15px;
            border-left: 4px solid #007bff;
        }

        .print-actions {
            text-align: center;
            margin: 15px 0;
        }

        .compact-row {
            margin-bottom: 8px;
        }

        .small-text {
            font-size: 0.85rem;
        }

        @media print {
            .print-actions {
                display: none;
            }

            body {
                margin: 0;
                padding: 0;
                font-size: 12px;
            }

            .invoice-container {
                max-width: none;
                margin: 0;
                padding: 10px;
            }

            .invoice-header {
                margin-bottom: 15px;
                padding-bottom: 10px;
            }

            .client-section {
                margin-bottom: 15px;
                padding: 10px;
            }

            .items-table {
                margin-bottom: 15px;
            }

            .totals-section {
                padding: 10px;
            }
        }

        @page {
            margin: 0.75in;
            size: A4;
        }
    </style>
</head>
<body>
    <div class="print-actions">
        <button onclick="window.print()" class="btn btn-primary btn-lg">
            <i class="fas fa-print"></i> Print Invoice
        </button>
        <button onclick="window.close()" class="btn btn-secondary btn-lg">
            <i class="fas fa-times"></i> Close
        </button>
    </div>

    <div class="invoice-container">
        <!-- Compact Invoice Header -->
        <div class="invoice-header">
            <div class="row align-items-center">
                <div class="col-md-7">
                    <div class="company-name"><?= e($invoice->company_name ?: 'Nice Techs') ?></div>
                    <div class="small-text">
                        <?php if ($invoice->company_address): ?>
                            <?= e($invoice->company_address) ?>
                        <?php endif; ?>
                        <?php if ($invoice->company_phone): ?>
                            | <i class="fas fa-phone"></i> <?= e($invoice->company_phone) ?>
                        <?php endif; ?>
                        <?php if ($invoice->company_email): ?>
                            | <i class="fas fa-envelope"></i> <?= e($invoice->company_email) ?>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="col-md-5 text-end">
                    <div class="invoice-title">INVOICE</div>
                    <div class="invoice-meta small-text">
                        <div class="compact-row"><strong>Invoice #:</strong> <?= e($invoice->invoice_number) ?></div>
                        <div class="compact-row"><strong>Date:</strong> <?= date('M j, Y', strtotime($invoice->invoice_date)) ?></div>
                        <?php if ($invoice->due_date): ?>
                            <div class="compact-row"><strong>Due Date:</strong> <?= date('M j, Y', strtotime($invoice->due_date)) ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Compact Bill To Section -->
        <div class="row">
            <div class="col-md-6">
                <div class="client-section">
                    <h6 class="mb-2"><strong>Bill To:</strong></h6>
                    <div class="client-info small-text">
                        <div class="fw-bold"><?= e($invoice->client_name) ?></div>
                        <?php if ($invoice->client_address): ?>
                            <div><?= nl2br(e($invoice->client_address)) ?></div>
                        <?php endif; ?>
                        <div class="mt-1">
                            <?php if ($invoice->client_phone): ?>
                                <i class="fas fa-phone"></i> <?= e($invoice->client_phone) ?>
                            <?php endif; ?>
                            <?php if ($invoice->client_email): ?>
                                <?php if ($invoice->client_phone): ?> | <?php endif; ?>
                                <i class="fas fa-envelope"></i> <?= e($invoice->client_email) ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <?php if (($invoice->paid_amount ?? 0) > 0 || ($invoice->payment_method ?? '')): ?>
            <div class="col-md-6">
                <div class="payment-info">
                    <h6 class="mb-2"><strong>Payment Information:</strong></h6>
                    <div class="small-text">
                        <?php if (($invoice->paid_amount ?? 0) > 0): ?>
                            <div><strong>Paid Amount:</strong> ₹<?= number_format($invoice->paid_amount, 2) ?></div>
                            <div><strong>Remaining:</strong> ₹<?= number_format(($invoice->total_amount ?? 0) - ($invoice->paid_amount ?? 0), 2) ?></div>
                        <?php endif; ?>
                        <?php if ($invoice->payment_method ?? ''): ?>
                            <div><strong>Method:</strong> <?= ucfirst(str_replace('_', ' ', e($invoice->payment_method))) ?></div>
                        <?php endif; ?>
                        <?php if ($invoice->payment_date ?? ''): ?>
                            <div><strong>Payment Date:</strong> <?= date('M j, Y', strtotime($invoice->payment_date)) ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Invoice Items -->
        <table class="table table-bordered items-table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th width="15%" class="text-center">Quantity</th>
                    <th width="20%" class="text-end">Unit Price</th>
                    <th width="20%" class="text-end">Total</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($items)): ?>
                    <tr>
                        <td colspan="4" class="text-center text-muted">No items found</td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($items as $item): ?>
                        <tr>
                            <td><?= e($item->description) ?></td>
                            <td class="text-center"><?= number_format($item->quantity, 2) ?></td>
                            <td class="text-end"><?= $item->getFormattedUnitPrice() ?></td>
                            <td class="text-end"><?= $item->getFormattedTotalPrice() ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>

        <!-- Compact Invoice Totals -->
        <div class="row">
            <div class="col-md-6">
                <?php if ($invoice->notes || $invoice->terms): ?>
                    <div class="small-text">
                        <?php if ($invoice->notes): ?>
                            <div class="mb-3">
                                <h6 class="mb-1"><strong>Notes:</strong></h6>
                                <p class="mb-0"><?= nl2br(e($invoice->notes)) ?></p>
                            </div>
                        <?php endif; ?>

                        <?php if ($invoice->terms): ?>
                            <div class="mb-3">
                                <h6 class="mb-1"><strong>Terms & Conditions:</strong></h6>
                                <p class="mb-0"><?= nl2br(e($invoice->terms)) ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>

            <div class="col-md-6">
                <div class="totals-section">
                    <table class="table table-sm mb-0 small-text">
                        <tr>
                            <td><strong>Subtotal:</strong></td>
                            <td class="text-end">₹<?= number_format($invoice->subtotal ?? 0, 2) ?></td>
                        </tr>
                        <?php if (($invoice->discount_amount ?? 0) > 0): ?>
                            <tr>
                                <td><strong>Discount:</strong></td>
                                <td class="text-end">-₹<?= number_format($invoice->discount_amount, 2) ?></td>
                            </tr>
                        <?php endif; ?>
                        <?php if (($invoice->tax_rate ?? 0) > 0): ?>
                            <tr>
                                <td><strong>Tax (<?= number_format($invoice->tax_rate, 2) ?>%):</strong></td>
                                <td class="text-end">₹<?= number_format($invoice->tax_amount ?? 0, 2) ?></td>
                            </tr>
                        <?php endif; ?>
                        <tr class="total-row">
                            <td><strong>Total Amount:</strong></td>
                            <td class="text-end"><strong>₹<?= number_format($invoice->total_amount ?? 0, 2) ?></strong></td>
                        </tr>
                        <?php if (($invoice->paid_amount ?? 0) > 0): ?>
                            <tr style="background-color: #d4edda;">
                                <td><strong>Paid Amount:</strong></td>
                                <td class="text-end"><strong>₹<?= number_format($invoice->paid_amount, 2) ?></strong></td>
                            </tr>
                            <tr style="background-color: #f8d7da;">
                                <td><strong>Remaining Amount:</strong></td>
                                <td class="text-end"><strong>₹<?= number_format(($invoice->total_amount ?? 0) - ($invoice->paid_amount ?? 0), 2) ?></strong></td>
                            </tr>
                        <?php endif; ?>
                    </table>
                </div>
            </div>
        </div>

        <!-- Compact Footer -->
        <div class="text-center mt-4 pt-3" style="border-top: 1px solid #ddd;">
            <p class="text-muted small-text mb-0">Thank you for your business!</p>
        </div>
    </div>

    <script>
        // Auto-print when page loads (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
