<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice <?= e($invoice->invoice_number) ?> - Print</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: white;
            color: #333;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .invoice-header {
            border-bottom: 3px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .company-name {
            font-size: 2.5rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .invoice-title {
            font-size: 3rem;
            font-weight: bold;
            color: #333;
            text-align: right;
        }
        
        .invoice-meta {
            font-size: 1.1rem;
        }
        
        .client-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        
        .items-table {
            margin-bottom: 30px;
        }
        
        .items-table th {
            background-color: #333;
            color: white;
            padding: 15px 10px;
        }
        
        .items-table td {
            padding: 12px 10px;
            border-bottom: 1px solid #ddd;
        }
        
        .totals-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
        }
        
        .total-row {
            font-size: 1.2rem;
            font-weight: bold;
            background-color: #333;
            color: white;
        }
        
        .print-actions {
            text-align: center;
            margin: 20px 0;
        }
        
        @media print {
            .print-actions {
                display: none;
            }
            
            body {
                margin: 0;
                padding: 0;
            }
            
            .invoice-container {
                max-width: none;
                margin: 0;
                padding: 0;
            }
        }
        
        @page {
            margin: 1in;
        }
    </style>
</head>
<body>
    <div class="print-actions">
        <button onclick="window.print()" class="btn btn-primary btn-lg">
            <i class="fas fa-print"></i> Print Invoice
        </button>
        <button onclick="window.close()" class="btn btn-secondary btn-lg">
            <i class="fas fa-times"></i> Close
        </button>
    </div>

    <div class="invoice-container">
        <!-- Invoice Header -->
        <div class="invoice-header">
            <div class="row">
                <div class="col-md-6">
                    <div class="company-name"><?= e($invoice->company_name ?: 'Your Company Name') ?></div>
                    <?php if ($invoice->company_address): ?>
                        <div class="company-address mb-2">
                            <?= nl2br(e($invoice->company_address)) ?>
                        </div>
                    <?php endif; ?>
                    <div class="company-contact">
                        <?php if ($invoice->company_phone): ?>
                            <div><i class="fas fa-phone"></i> <?= e($invoice->company_phone) ?></div>
                        <?php endif; ?>
                        <?php if ($invoice->company_email): ?>
                            <div><i class="fas fa-envelope"></i> <?= e($invoice->company_email) ?></div>
                        <?php endif; ?>
                        <?php if ($invoice->company_website): ?>
                            <div><i class="fas fa-globe"></i> <?= e($invoice->company_website) ?></div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="invoice-title">INVOICE</div>
                    <div class="invoice-meta mt-3">
                        <div class="mb-2">
                            <strong>Invoice #:</strong> <?= e($invoice->invoice_number) ?>
                        </div>
                        <div class="mb-2">
                            <strong>Date:</strong> <?= date('M j, Y', strtotime($invoice->invoice_date)) ?>
                        </div>
                        <?php if ($invoice->due_date): ?>
                            <div class="mb-2">
                                <strong>Due Date:</strong> <?= date('M j, Y', strtotime($invoice->due_date)) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bill To Section -->
        <div class="client-section">
            <h5 class="mb-3"><strong>Bill To:</strong></h5>
            <div class="client-info">
                <div class="h6"><strong><?= e($invoice->client_name) ?></strong></div>
                <?php if ($invoice->client_address): ?>
                    <div class="mt-2"><?= nl2br(e($invoice->client_address)) ?></div>
                <?php endif; ?>
                <?php if ($invoice->client_phone): ?>
                    <div class="mt-1"><i class="fas fa-phone"></i> <?= e($invoice->client_phone) ?></div>
                <?php endif; ?>
                <?php if ($invoice->client_email): ?>
                    <div class="mt-1"><i class="fas fa-envelope"></i> <?= e($invoice->client_email) ?></div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Invoice Items -->
        <table class="table table-bordered items-table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th width="15%" class="text-center">Quantity</th>
                    <th width="20%" class="text-end">Unit Price</th>
                    <th width="20%" class="text-end">Total</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($items)): ?>
                    <tr>
                        <td colspan="4" class="text-center text-muted">No items found</td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($items as $item): ?>
                        <tr>
                            <td><?= e($item->description) ?></td>
                            <td class="text-center"><?= number_format($item->quantity, 2) ?></td>
                            <td class="text-end"><?= $item->getFormattedUnitPrice() ?></td>
                            <td class="text-end"><?= $item->getFormattedTotalPrice() ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>

        <!-- Invoice Totals -->
        <div class="row">
            <div class="col-md-6">
                <?php if ($invoice->notes): ?>
                    <div class="mb-4">
                        <h6><strong>Notes:</strong></h6>
                        <p><?= nl2br(e($invoice->notes)) ?></p>
                    </div>
                <?php endif; ?>
                
                <?php if ($invoice->terms): ?>
                    <div class="mb-4">
                        <h6><strong>Terms & Conditions:</strong></h6>
                        <p><?= nl2br(e($invoice->terms)) ?></p>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="col-md-6">
                <div class="totals-section">
                    <table class="table table-sm mb-0">
                        <tr>
                            <td><strong>Subtotal:</strong></td>
                            <td class="text-end">₹<?= number_format($invoice->subtotal, 2) ?></td>
                        </tr>
                        <?php if ($invoice->discount_amount > 0): ?>
                            <tr>
                                <td><strong>Discount:</strong></td>
                                <td class="text-end">-₹<?= number_format($invoice->discount_amount, 2) ?></td>
                            </tr>
                        <?php endif; ?>
                        <?php if ($invoice->tax_rate > 0): ?>
                            <tr>
                                <td><strong>Tax (<?= number_format($invoice->tax_rate, 2) ?>%):</strong></td>
                                <td class="text-end">₹<?= number_format($invoice->tax_amount, 2) ?></td>
                            </tr>
                        <?php endif; ?>
                        <tr class="total-row">
                            <td><strong>Total Amount:</strong></td>
                            <td class="text-end"><strong><?= $invoice->getFormattedTotal() ?></strong></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-5 pt-4" style="border-top: 1px solid #ddd;">
            <p class="text-muted">Thank you for your business!</p>
        </div>
    </div>

    <script>
        // Auto-print when page loads (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
