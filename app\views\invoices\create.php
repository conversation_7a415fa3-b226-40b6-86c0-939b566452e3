<?php
ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-plus-circle"></i>
                        Create New Invoice
                    </h3>
                    <div class="card-tools">
                        <a href="<?= url('/invoices') ?>" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Invoices
                        </a>
                    </div>
                </div>

                <form id="invoiceForm" action="<?= url('/invoices') ?>" method="POST">
                    <div class="card-body">
                        <!-- Invoice Header -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5>Invoice Details</h5>
                                <div class="mb-3">
                                    <label for="invoice_number" class="form-label">Invoice Number</label>
                                    <input type="text" name="invoice_number" id="invoice_number"
                                           class="form-control" value="<?= e($invoiceNumber) ?>" readonly>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="invoice_date" class="form-label">Invoice Date</label>
                                            <input type="date" name="invoice_date" id="invoice_date"
                                                   class="form-control" value="<?= date('Y-m-d') ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="due_date" class="form-label">Due Date</label>
                                            <input type="date" name="due_date" id="due_date"
                                                   class="form-control" value="<?= date('Y-m-d', strtotime('+30 days')) ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <h5>Company Information</h5>
                                <div class="mb-3">
                                    <label for="company_name" class="form-label">Company Name</label>
                                    <input type="text" name="company_name" id="company_name"
                                           class="form-control" value="Your Company Name">
                                </div>
                                <div class="mb-3">
                                    <label for="company_address" class="form-label">Company Address</label>
                                    <textarea name="company_address" id="company_address"
                                              class="form-control" rows="3">Your Company Address</textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="company_phone" class="form-label">Phone</label>
                                            <input type="text" name="company_phone" id="company_phone"
                                                   class="form-control" value="+91-XXXXXXXXXX">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="company_email" class="form-label">Email</label>
                                            <input type="email" name="company_email" id="company_email"
                                                   class="form-control" value="<EMAIL>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <!-- Client Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5>Bill To</h5>
                                <div class="mb-3">
                                    <label for="client_id" class="form-label">Select Client</label>
                                    <select name="client_id" id="client_id" class="form-control">
                                        <option value="">Select existing client or enter manually</option>
                                        <?php if (isset($clients)): ?>
                                            <?php foreach ($clients as $client): ?>
                                                <option value="<?= e($client->id) ?>"
                                                        data-name="<?= e($client->name) ?>"
                                                        data-email="<?= e($client->email) ?>"
                                                        data-phone="<?= e($client->phone) ?>"
                                                        data-address="<?= e($client->address) ?>">
                                                    <?= e($client->name) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="client_name" class="form-label">Client Name *</label>
                                    <input type="text" name="client_name" id="client_name"
                                           class="form-control" required>
                                </div>
                                <div class="mb-3">
                                    <label for="client_email" class="form-label">Client Email</label>
                                    <input type="email" name="client_email" id="client_email"
                                           class="form-control">
                                </div>
                                <div class="mb-3">
                                    <label for="client_phone" class="form-label">Client Phone</label>
                                    <input type="text" name="client_phone" id="client_phone"
                                           class="form-control">
                                </div>
                                <div class="mb-3">
                                    <label for="client_address" class="form-label">Client Address</label>
                                    <textarea name="client_address" id="client_address"
                                              class="form-control" rows="3"></textarea>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <!-- Invoice Items -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5>Invoice Items</h5>
                                <button type="button" class="btn btn-success btn-sm" onclick="addInvoiceItem()">
                                    <i class="fas fa-plus"></i> Add Item
                                </button>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-bordered" id="invoiceItemsTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="40%">Description</th>
                                            <th width="15%">Quantity</th>
                                            <th width="20%">Unit Price (₹)</th>
                                            <th width="20%">Total (₹)</th>
                                            <th width="5%">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody id="invoiceItemsBody">
                                        <tr class="invoice-item-row">
                                            <td>
                                                <input type="text" name="items[0][description]"
                                                       class="form-control" placeholder="Item description" required>
                                            </td>
                                            <td>
                                                <input type="number" name="items[0][quantity]"
                                                       class="form-control quantity-input"
                                                       value="1" min="0.01" step="0.01" required>
                                            </td>
                                            <td>
                                                <input type="number" name="items[0][unit_price]"
                                                       class="form-control unit-price-input"
                                                       min="0.01" step="0.01" required>
                                            </td>
                                            <td>
                                                <input type="number" name="items[0][total_price]"
                                                       class="form-control total-price-input"
                                                       readonly>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-danger btn-sm"
                                                        onclick="removeInvoiceItem(this)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Invoice Totals -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">Notes</label>
                                    <textarea name="notes" id="notes" class="form-control" rows="3"
                                              placeholder="Additional notes or comments"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="terms" class="form-label">Terms & Conditions</label>
                                    <textarea name="terms" id="terms" class="form-control" rows="3"
                                              placeholder="Payment terms and conditions">Payment due within 30 days of invoice date.</textarea>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">Invoice Summary</h6>

                                        <div class="row mb-2">
                                            <div class="col-6">Subtotal:</div>
                                            <div class="col-6 text-end">
                                                ₹<span id="subtotalDisplay">0.00</span>
                                                <input type="hidden" name="subtotal" id="subtotal" value="0">
                                            </div>
                                        </div>

                                        <div class="row mb-2">
                                            <div class="col-6">
                                                <label for="discount_amount">Discount (₹):</label>
                                            </div>
                                            <div class="col-6">
                                                <input type="number" name="discount_amount" id="discount_amount"
                                                       class="form-control form-control-sm" value="0" min="0" step="0.01">
                                            </div>
                                        </div>

                                        <div class="row mb-2">
                                            <div class="col-6">
                                                <label for="tax_rate">Tax Rate (%):</label>
                                            </div>
                                            <div class="col-6">
                                                <input type="number" name="tax_rate" id="tax_rate"
                                                       class="form-control form-control-sm" value="18" min="0" max="100" step="0.01">
                                            </div>
                                        </div>

                                        <div class="row mb-2">
                                            <div class="col-6">Tax Amount:</div>
                                            <div class="col-6 text-end">
                                                ₹<span id="taxAmountDisplay">0.00</span>
                                                <input type="hidden" name="tax_amount" id="tax_amount" value="0">
                                            </div>
                                        </div>

                                        <hr>

                                        <div class="row">
                                            <div class="col-6"><strong>Total Amount:</strong></div>
                                            <div class="col-6 text-end">
                                                <strong>₹<span id="totalAmountDisplay">0.00</span></strong>
                                                <input type="hidden" name="total_amount" id="total_amount" value="0" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <a href="<?= url('/invoices') ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <div>
                                <button type="submit" name="status" value="draft" class="btn btn-warning">
                                    <i class="fas fa-save"></i> Save as Draft
                                </button>
                                <button type="submit" name="status" value="sent" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i> Create & Send
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
let itemCounter = 1;

// Auto-fill client details when selected
document.getElementById('client_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    if (selectedOption.value) {
        document.getElementById('client_name').value = selectedOption.dataset.name || '';
        document.getElementById('client_email').value = selectedOption.dataset.email || '';
        document.getElementById('client_phone').value = selectedOption.dataset.phone || '';
        document.getElementById('client_address').value = selectedOption.dataset.address || '';
    }
});

// Add new invoice item
function addInvoiceItem() {
    const tbody = document.getElementById('invoiceItemsBody');
    const newRow = document.createElement('tr');
    newRow.className = 'invoice-item-row';
    newRow.innerHTML = `
        <td>
            <input type="text" name="items[${itemCounter}][description]"
                   class="form-control" placeholder="Item description" required>
        </td>
        <td>
            <input type="number" name="items[${itemCounter}][quantity]"
                   class="form-control quantity-input"
                   value="1" min="0.01" step="0.01" required>
        </td>
        <td>
            <input type="number" name="items[${itemCounter}][unit_price]"
                   class="form-control unit-price-input"
                   min="0.01" step="0.01" required>
        </td>
        <td>
            <input type="number" name="items[${itemCounter}][total_price]"
                   class="form-control total-price-input"
                   readonly>
        </td>
        <td>
            <button type="button" class="btn btn-danger btn-sm"
                    onclick="removeInvoiceItem(this)">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
    tbody.appendChild(newRow);
    itemCounter++;

    // Add event listeners to new inputs
    addCalculationListeners(newRow);
}

// Remove invoice item
function removeInvoiceItem(button) {
    const row = button.closest('tr');
    if (document.querySelectorAll('.invoice-item-row').length > 1) {
        row.remove();
        calculateTotals();
    } else {
        alert('At least one item is required.');
    }
}

// Add calculation event listeners
function addCalculationListeners(row) {
    const quantityInput = row.querySelector('.quantity-input');
    const unitPriceInput = row.querySelector('.unit-price-input');

    quantityInput.addEventListener('input', calculateRowTotal);
    unitPriceInput.addEventListener('input', calculateRowTotal);
}

// Calculate row total
function calculateRowTotal(event) {
    const row = event.target.closest('tr');
    const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
    const unitPrice = parseFloat(row.querySelector('.unit-price-input').value) || 0;
    const total = quantity * unitPrice;

    row.querySelector('.total-price-input').value = total.toFixed(2);
    calculateTotals();
}

// Calculate invoice totals
function calculateTotals() {
    let subtotal = 0;

    document.querySelectorAll('.total-price-input').forEach(input => {
        subtotal += parseFloat(input.value) || 0;
    });

    const discount = parseFloat(document.getElementById('discount_amount').value) || 0;
    const taxRate = parseFloat(document.getElementById('tax_rate').value) || 0;

    const taxableAmount = subtotal - discount;
    const taxAmount = taxableAmount * (taxRate / 100);
    const totalAmount = taxableAmount + taxAmount;

    document.getElementById('subtotalDisplay').textContent = subtotal.toFixed(2);
    document.getElementById('subtotal').value = subtotal.toFixed(2);

    document.getElementById('taxAmountDisplay').textContent = taxAmount.toFixed(2);
    document.getElementById('tax_amount').value = taxAmount.toFixed(2);

    document.getElementById('totalAmountDisplay').textContent = totalAmount.toFixed(2);
    document.getElementById('total_amount').value = totalAmount.toFixed(2);
}

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Add listeners to existing rows
    document.querySelectorAll('.invoice-item-row').forEach(addCalculationListeners);

    // Add listeners to discount and tax inputs
    document.getElementById('discount_amount').addEventListener('input', calculateTotals);
    document.getElementById('tax_rate').addEventListener('input', calculateTotals);

    // Initial calculation
    calculateTotals();
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_PATH . '/views/layouts/app.php';
?>
