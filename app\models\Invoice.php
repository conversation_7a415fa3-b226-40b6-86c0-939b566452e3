<?php

class Invoice extends Model
{
    protected $table = 'invoices';
    protected $fillable = [
        'invoice_number', 'client_id', 'client_name', 'client_email', 'client_phone', 'client_address',
        'company_name', 'company_address', 'company_phone', 'company_email', 'company_website',
        'invoice_date', 'due_date', 'subtotal', 'tax_rate', 'tax_amount', 'discount_amount',
        'total_amount', 'notes', 'terms', 'status'
    ];

    // Relationship with client
    public function client()
    {
        return $this->belongsTo('Client', 'client_id');
    }

    // Relationship with invoice items
    public function items()
    {
        return $this->hasMany('InvoiceItem', 'invoice_id');
    }

    // Generate unique invoice number
    public static function generateInvoiceNumber()
    {
        $year = date('Y');
        $month = date('m');
        $prefix = "INV-{$year}{$month}-";

        // Get the last invoice number for this month
        $db = Database::getInstance()->getConnection();
        $stmt = $db->prepare("SELECT invoice_number FROM invoices WHERE invoice_number LIKE ? ORDER BY invoice_number DESC LIMIT 1");
        $stmt->execute([$prefix . '%']);
        $lastInvoice = $stmt->fetch(PDO::FETCH_OBJ);

        if ($lastInvoice) {
            $lastNumber = intval(substr($lastInvoice->invoice_number, -4));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    // Calculate totals
    public function calculateTotals()
    {
        $items = $this->items();
        $subtotal = 0;

        foreach ($items as $item) {
            $subtotal += $item->total_price;
        }

        $this->subtotal = $subtotal;
        $this->tax_amount = ($subtotal - $this->discount_amount) * ($this->tax_rate / 100);
        $this->total_amount = $subtotal - $this->discount_amount + $this->tax_amount;

        return $this;
    }

    // Get formatted total
    public function getFormattedTotal()
    {
        return '₹' . number_format($this->total_amount, 2);
    }

    // Get status badge class
    public function getStatusBadgeClass()
    {
        switch ($this->status) {
            case 'paid':
                return 'bg-success';
            case 'sent':
                return 'bg-info';
            case 'overdue':
                return 'bg-danger';
            case 'cancelled':
                return 'bg-secondary';
            default:
                return 'bg-warning';
        }
    }

    // Check if invoice is overdue
    public function isOverdue()
    {
        return $this->due_date && $this->due_date < date('Y-m-d') && $this->status !== 'paid';
    }
}
