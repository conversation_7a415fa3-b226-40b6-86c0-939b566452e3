<?php

class InvoiceController extends Controller
{
    // Display a list of invoices
    public function index()
    {
        $invoiceModel = new Invoice();
        $invoicesData = $invoiceModel->all('created_at DESC');

        // Convert to Invoice objects
        $invoices = [];
        foreach ($invoicesData as $invoiceData) {
            $invoice = new Invoice();
            $invoice->fill($invoiceData);
            $invoice->id = $invoiceData['id'];

            // Update overdue status if needed
            if ($invoice->isOverdue() && $invoice->status !== 'overdue') {
                $invoice->status = 'overdue';
                $invoice->save();
            }

            $invoices[] = $invoice;
        }

        echo $this->view('invoices/index', ['invoices' => $invoices]);
    }

    // Show the form to create a new invoice
    public function create()
    {
        $clientModel = new Client();
        $clients = $clientModel->where(['is_active' => true], 'name');
        $invoiceNumber = Invoice::generateInvoiceNumber();

        echo $this->view('invoices/create', [
            'clients' => $clients,
            'invoiceNumber' => $invoiceNumber
        ]);
    }

    // Store a new invoice
    public function store()
    {
        try {
            $data = $_POST;

            // Validation
            if (empty($data['client_name']) || empty($data['total_amount'])) {
                $this->setFlashMessage('Client name and total amount are required.', 'error');
                redirect('/invoices/create');
                return;
            }

            // Generate invoice number if not provided
            if (empty($data['invoice_number'])) {
                $data['invoice_number'] = Invoice::generateInvoiceNumber();
            }

            // Set default dates
            if (empty($data['invoice_date'])) {
                $data['invoice_date'] = date('Y-m-d');
            }

            if (empty($data['due_date'])) {
                $data['due_date'] = date('Y-m-d', strtotime('+30 days'));
            }

            // Create invoice
            $invoice = new Invoice($data);
            $invoice->save();

            // Save invoice items if provided
            if (!empty($data['items'])) {
                foreach ($data['items'] as $itemData) {
                    if (!empty($itemData['description']) && !empty($itemData['quantity']) && !empty($itemData['unit_price'])) {
                        $item = new InvoiceItem([
                            'invoice_id' => $invoice->id,
                            'description' => $itemData['description'],
                            'quantity' => $itemData['quantity'],
                            'unit_price' => $itemData['unit_price']
                        ]);
                        $item->calculateTotal();
                        $item->save();
                    }
                }

                // Recalculate invoice totals
                $invoice->calculateTotals();
                $invoice->save();
            }

            $this->setFlashMessage('Invoice created successfully.', 'success');
            $this->redirect('/invoices/' . $invoice->id);

        } catch (Exception $e) {
            $this->setFlashMessage('Error creating invoice: ' . $e->getMessage(), 'error');
            $this->redirect('/invoices/create');
        }
    }

    // Display a specific invoice
    public function show($id)
    {
        $invoiceModel = new Invoice();
        $invoiceData = $invoiceModel->find($id);
        if (!$invoiceData) {
            $this->setFlashMessage('Invoice not found.', 'error');
            $this->redirect('/invoices');
            return;
        }

        // Convert to Invoice object
        $invoice = new Invoice();
        $invoice->fill($invoiceData);
        $invoice->id = $invoiceData['id'];

        $itemModel = new InvoiceItem();
        $itemsData = $itemModel->where(['invoice_id' => $id]);

        // Convert to InvoiceItem objects
        $items = [];
        foreach ($itemsData as $itemData) {
            $item = new InvoiceItem();
            $item->fill($itemData);
            $item->id = $itemData['id'];
            $items[] = $item;
        }

        echo $this->view('invoices/show', [
            'invoice' => $invoice,
            'items' => $items
        ]);
    }

    // Show the form to edit an invoice
    public function edit($id)
    {
        $invoiceModel = new Invoice();
        $invoiceData = $invoiceModel->find($id);
        if (!$invoiceData) {
            $this->setFlashMessage('Invoice not found.', 'error');
            $this->redirect('/invoices');
            return;
        }

        // Convert to Invoice object
        $invoice = new Invoice();
        $invoice->fill($invoiceData);
        $invoice->id = $invoiceData['id'];

        $clientModel = new Client();
        $clients = $clientModel->where(['is_active' => true], 'name');

        $itemModel = new InvoiceItem();
        $itemsData = $itemModel->where(['invoice_id' => $id]);

        // Convert to InvoiceItem objects
        $items = [];
        foreach ($itemsData as $itemData) {
            $item = new InvoiceItem();
            $item->fill($itemData);
            $item->id = $itemData['id'];
            $items[] = $item;
        }

        echo $this->view('invoices/edit', [
            'invoice' => $invoice,
            'clients' => $clients,
            'items' => $items
        ]);
    }

    // Update an invoice
    public function update($id)
    {
        try {
            $invoiceModel = new Invoice();
            $invoice = $invoiceModel->find($id);
            if (!$invoice) {
                $this->setFlashMessage('Invoice not found.', 'error');
                $this->redirect('/invoices');
                return;
            }

            $data = $_POST;

            // Update invoice
            $invoiceObj = new Invoice();
            $invoiceObj->fill($data);
            $invoiceObj->id = $id;
            $invoiceObj->save();

            // Update invoice items
            if (!empty($data['items'])) {
                // Delete existing items
                $db = Database::getInstance()->getConnection();
                $stmt = $db->prepare("DELETE FROM invoice_items WHERE invoice_id = ?");
                $stmt->execute([$id]);

                // Add new items
                foreach ($data['items'] as $itemData) {
                    if (!empty($itemData['description']) && !empty($itemData['quantity']) && !empty($itemData['unit_price'])) {
                        $item = new InvoiceItem();
                        $item->fill([
                            'invoice_id' => $id,
                            'description' => $itemData['description'],
                            'quantity' => $itemData['quantity'],
                            'unit_price' => $itemData['unit_price']
                        ]);
                        $item->calculateTotal();
                        $item->save();
                    }
                }
            }

            $this->setFlashMessage('Invoice updated successfully.', 'success');
            $this->redirect('/invoices/' . $id);

        } catch (Exception $e) {
            $this->setFlashMessage('Error updating invoice: ' . $e->getMessage(), 'error');
            $this->redirect('/invoices/' . $id . '/edit');
        }
    }

    // Delete an invoice
    public function delete($id)
    {
        try {
            $invoiceModel = new Invoice();
            $invoice = $invoiceModel->find($id);
            if (!$invoice) {
                $this->setFlashMessage('Invoice not found.', 'error');
                $this->redirect('/invoices');
                return;
            }

            $invoiceObj = new Invoice();
            $invoiceObj->id = $id;
            $invoiceObj->delete();
            $this->setFlashMessage('Invoice deleted successfully.', 'success');
            $this->redirect('/invoices');

        } catch (Exception $e) {
            $this->setFlashMessage('Error deleting invoice: ' . $e->getMessage(), 'error');
            $this->redirect('/invoices');
        }
    }

    // Print invoice
    public function print($id)
    {
        $invoiceModel = new Invoice();
        $invoiceData = $invoiceModel->find($id);
        if (!$invoiceData) {
            $this->setFlashMessage('Invoice not found.', 'error');
            $this->redirect('/invoices');
            return;
        }

        // Convert to Invoice object
        $invoice = new Invoice();
        $invoice->fill($invoiceData);
        $invoice->id = $invoiceData['id'];

        $itemModel = new InvoiceItem();
        $itemsData = $itemModel->where(['invoice_id' => $id]);

        // Convert to InvoiceItem objects
        $items = [];
        foreach ($itemsData as $itemData) {
            $item = new InvoiceItem();
            $item->fill($itemData);
            $item->id = $itemData['id'];
            $items[] = $item;
        }

        echo $this->view('invoices/print', [
            'invoice' => $invoice,
            'items' => $items
        ]);
    }

    // Update invoice status
    public function updateStatus($id)
    {
        try {
            $invoiceModel = new Invoice();
            $invoice = $invoiceModel->find($id);
            if (!$invoice) {
                echo json_encode(['success' => false, 'message' => 'Invoice not found']);
                return;
            }

            $status = $_POST['status'] ?? '';
            if (in_array($status, ['draft', 'sent', 'paid', 'overdue', 'cancelled'])) {
                $invoiceObj = new Invoice();
                $invoiceObj->id = $id;
                $invoiceObj->status = $status;
                $invoiceObj->save();

                echo json_encode(['success' => true, 'message' => 'Status updated successfully']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Invalid status']);
            }

        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Error updating status: ' . $e->getMessage()]);
        }
    }
}
