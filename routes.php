<?php
/**
 * Application Routes
 */

$router = new Router();

// Home route
$router->get('/', 'HomeController@index');

// Authentication routes
$router->group(['middleware' => 'guest'], function($router) {
    $router->get('/login', 'AuthController@login');
    $router->post('/login', 'AuthController@authenticate');
});

$router->get('/logout', 'AuthController@logout');

$router->group(['middleware' => 'auth'], function($router) {
    // Invoices
    $router->get('/invoices', 'InvoiceController@index');
    $router->get('/invoices/create', 'InvoiceController@create');
    $router->post('/invoices', 'InvoiceController@store');
    $router->get('/invoices/{id}', 'InvoiceController@show');
    $router->get('/invoices/{id}/edit', 'InvoiceController@edit');
    $router->put('/invoices/{id}', 'InvoiceController@update');
    $router->delete('/invoices/{id}', 'InvoiceController@delete');
    
    // Dashboard
    $router->get('/dashboard', 'DashboardController@index');
    $router->get('/api/dashboard/chart-data', 'DashboardController@getChartData');
    
    // Clients
    $router->get('/clients', 'ClientController@index');
    $router->get('/clients/create', 'ClientController@create');
    $router->post('/clients', 'ClientController@store');
    $router->get('/clients/{id}', 'ClientController@show');
    $router->get('/clients/{id}/edit', 'ClientController@edit');
    $router->put('/clients/{id}', 'ClientController@update');
    $router->delete('/clients/{id}', 'ClientController@delete');
    $router->get('/clients/export', 'ClientController@export');
    
    // Domains
    $router->get('/domains', 'DomainController@index');
    $router->get('/domains/create', 'DomainController@create');
    $router->post('/domains', 'DomainController@store');
    $router->get('/domains/{id}', 'DomainController@show');
    $router->get('/domains/{id}/edit', 'DomainController@edit');
    $router->put('/domains/{id}', 'DomainController@update');
    $router->delete('/domains/{id}', 'DomainController@delete');
    $router->post('/domains/bulk-update', 'DomainController@bulkUpdate');
    $router->get('/domains/export', 'DomainController@export');
    $router->get('/domains/client-domains', 'DomainController@getClientDomains');
    $router->get('/domains/{id}/whatsapp-data', 'DomainController@getWhatsAppData');

    // API Routes
    $router->get('/api/test', 'ApiController@test');
    $router->get('/api/client-domains', 'ApiController@getClientDomains');
    
    // Servers
    $router->get('/servers', 'ServerController@index');
    $router->get('/servers/create', 'ServerController@create');
    $router->post('/servers', 'ServerController@store');
    $router->get('/servers/{id}', 'ServerController@show');
    $router->get('/servers/{id}/edit', 'ServerController@edit');
    $router->put('/servers/{id}', 'ServerController@update');
    $router->delete('/servers/{id}', 'ServerController@delete');
    $router->get('/servers/export', 'ServerController@export');
    $router->get('/servers/{id}/whatsapp-data', 'ServerController@getWhatsAppData');
    
    // Mediators
    $router->get('/mediators', 'MediatorController@index');
    $router->get('/mediators/create', 'MediatorController@create');
    $router->post('/mediators', 'MediatorController@store');
    $router->get('/mediators/{id}', 'MediatorController@show');
    $router->get('/mediators/{id}/edit', 'MediatorController@edit');
    $router->put('/mediators/{id}', 'MediatorController@update');
    $router->delete('/mediators/{id}', 'MediatorController@delete');
    $router->get('/mediators/{id}/commission-report', 'MediatorController@commissionReport');
    $router->get('/mediators/export', 'MediatorController@export');
    
    // Reports
    $router->get('/reports', 'ReportController@index');
    $router->get('/reports/domain-expiry', 'ReportController@domainExpiry');
    $router->get('/reports/server-expiry', 'ReportController@serverExpiry');
    $router->get('/reports/revenue', 'ReportController@revenue');
    $router->get('/reports/client-summary', 'ReportController@clientSummary');
    $router->get('/reports/renewal-calendar', 'ReportController@renewalCalendar');
    
    // Admin routes
    $router->group(['middleware' => 'admin'], function($router) {
        
        // User Management
        $router->get('/users', 'UserController@index');
        $router->get('/users/create', 'UserController@create');
        $router->post('/users', 'UserController@store');
        $router->get('/users/{id}', 'UserController@show');
        $router->get('/users/{id}/edit', 'UserController@edit');
        $router->put('/users/{id}', 'UserController@update');
        $router->delete('/users/{id}', 'UserController@delete');
        $router->post('/users/{id}/toggle-status', 'UserController@toggleStatus');
        $router->post('/users/{id}/reset-password', 'UserController@resetPassword');
        
        // Settings
        $router->get('/settings', 'SettingsController@index');
        $router->get('/settings/application', 'SettingsController@application');
        $router->post('/settings/application', 'SettingsController@updateApplication');
        $router->get('/settings/email', 'SettingsController@email');
        $router->post('/settings/email', 'SettingsController@updateEmail');
        $router->post('/settings/email/test', 'SettingsController@testEmail');
        $router->get('/settings/notifications', 'SettingsController@notifications');
        $router->post('/settings/notifications', 'SettingsController@updateNotifications');
        $router->get('/settings/cache', 'SettingsController@cache');
        $router->post('/settings/cache/clear', 'SettingsController@clearCache');
        $router->post('/settings/cache/clean', 'SettingsController@cleanCache');
        $router->get('/settings/backup', 'SettingsController@backup');
        $router->post('/settings/backup/create', 'SettingsController@createBackup');
        $router->get('/settings/backup/download/{filename}', 'SettingsController@downloadBackup');
        $router->delete('/settings/backup/{filename}', 'SettingsController@deleteBackup');
    });
    
    // API routes
    $router->get('/api/notifications', function() {
        // Get unread notifications for current user
        $db = Database::getInstance()->getConnection();
        $stmt = $db->prepare("
            SELECT * FROM notifications 
            WHERE (user_id = ? OR user_id IS NULL) AND is_read = 0 
            ORDER BY created_at DESC 
            LIMIT 10
        ");
        $stmt->execute([Auth::id()]);
        $notifications = $stmt->fetchAll();
        
        $unreadCount = count($notifications);
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'notifications' => $notifications,
            'unread_count' => $unreadCount
        ]);
        exit;
    });
    
    $router->post('/api/notifications/mark-read', function() {
        $id = $_POST['id'] ?? null;
        if ($id) {
            $db = Database::getInstance()->getConnection();
            $stmt = $db->prepare("UPDATE notifications SET is_read = 1 WHERE id = ?");
            $stmt->execute([$id]);
        }
        
        header('Content-Type: application/json');
        echo json_encode(['success' => true]);
        exit;
    });
    
    $router->get('/api/system-status', function() {
        // Simple system status check
        $status = 'online';
        
        try {
            // Check database connection
            $db = Database::getInstance()->getConnection();
            $db->query("SELECT 1");
        } catch (Exception $e) {
            $status = 'offline';
        }
        
        header('Content-Type: application/json');
        echo json_encode(['status' => $status]);
        exit;
    });
});

// Profile routes (accessible by all authenticated users)
$router->group(['middleware' => 'auth'], function($router) {
    $router->get('/profile', 'ProfileController@index');
    $router->post('/profile', 'ProfileController@update');
});

// Dispatch the router
$router->dispatch();
